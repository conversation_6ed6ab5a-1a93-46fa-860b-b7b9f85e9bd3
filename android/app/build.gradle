plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace = "com.example.tangazoletusacco"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    buildFeatures {
        buildConfig = true
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.tangazoletusacco"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 21
        targetSdk = flutter.targetSdkVersion
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
        // Define manifest placeholders
        manifestPlaceholders = [
            'applicationName': 'io.flutter.app.FlutterApplication'
        ]
    }

    flavorDimensions = ["client"]
    productFlavors {
        tower {
            dimension "client"
            buildConfigField "String", "CLIENT_ID", '"81"'
            applicationIdSuffix ".tower"
            versionNameSuffix "-tower"
        }
        mentor {
            dimension "client"
            buildConfigField "String", "CLIENT_ID", '"38"'
            applicationIdSuffix ".mentor"
            versionNameSuffix "-mentor"
        }
        amica {
            dimension "client"
            buildConfigField "String", "CLIENT_ID", '"116"'
            applicationIdSuffix ".amica"
            versionNameSuffix "-amica"
        }
        magadi {
            dimension "client"
            buildConfigField "String", "CLIENT_ID", '"120"'
            applicationIdSuffix ".magadi"
            versionNameSuffix "-magadi"
        }
        mboresha {
            dimension "client"
            buildConfigField "String", "CLIENT_ID", '"93"'
            applicationIdSuffix ".mboresha"
            versionNameSuffix "-mboresha"
        }
        tangazoletu {
            dimension "client"
            buildConfigField "String", "CLIENT_ID", '"999"'
            applicationIdSuffix ".tangazoletu"
            versionNameSuffix "-tangazoletu"
        }
        shelloyees {
            dimension "client"
            buildConfigField "String", "CLIENT_ID", '"114"'
            applicationIdSuffix ".shelloyees"
            versionNameSuffix "-shelloyees"
        }
        ollin {
            dimension "client"
            buildConfigField "String", "CLIENT_ID", '"54"'
            applicationIdSuffix ".ollin"
            versionNameSuffix "-ollin"
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
}
