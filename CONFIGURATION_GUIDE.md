# Sacco Multi-Client Configuration System Guide

This comprehensive guide explains how to use the configurable system that allows one codebase to support multiple Saccos with different branding, colors, and configurations.

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture](#architecture)
3. [Current Client Configurations](#current-client-configurations)
4. [Adding a New Client](#adding-a-new-client)
5. [Building Different Flavors](#building-different-flavors)
6. [Using Configurable Themes in Code](#using-configurable-themes-in-code)
7. [Asset Management](#asset-management)
8. [Environment Configuration](#environment-configuration)
9. [Testing and Debugging](#testing-and-debugging)
10. [Troubleshooting](#troubleshooting)
11. [Best Practices](#best-practices)

## System Overview

The app supports multiple Sacco configurations through a sophisticated build flavor and theme management system. Each client can have:

- **Unique branding**: Colors, logos, fonts, splash screens
- **Custom app names**: Different display names per client
- **Separate app IDs**: Allows multiple versions on same device
- **Environment-specific configurations**: Different API endpoints per client
- **Asset customization**: Client-specific images, icons, and resources

### Currently Supported Clients

| Client ID | Client Name | Primary Color | Secondary Color | Build Flavor |
|-----------|-------------|---------------|-----------------|--------------|
| 54        | Ollin Sacco | #00AEEF (Blue) | #F58220 (Orange) | `ollin` |
| 81        | Tower Sacco | #00A651 (Green) | #EC008C (Pink) | `tower` |
| 38        | Mentor Cash | #F38B32 (Orange) | #067C4B (Green) | `mentor` |
| 93        | M-BORESHA | #388515 (Green) | #B1D848 (Light Green) | `mboresha` |
| 116       | Amica Sacco | #2648B6 (Blue) | #87CEFA (Light Blue) | `amica` |
| 120       | Magadi Sacco | #234e87 (Blue) | #9b3915 (Red) | `magadi` |
| 999       | Tangazoletu Sacco | #3987CA (Blue) | #8CC543 (Green) | `tangazoletu` |

## Architecture

### Core Components

#### 1. Configuration Classes (`lib/configuration/`)

**`client_config.dart`** - Defines data models:
```dart
class ClientConfig {
  final String clientId;
  final String displayName;
  final ClientColorPalette colors;
  final String? logoAsset;
  final String? fontFamily;
  final SplashScreenConfig splashConfig;
}

class ClientColorPalette {
  final Color primary, secondary, background, surface;
  final Color error, success, warning, info;
  final Color textPrimary, textSecondary, textDisabled;
  final Color modalBackground, unselectedNavItem;
}
```

**`client_configurations.dart`** - Registers all client configurations:
- Contains registration methods for each client
- Handles dynamic client registration
- Manages fallback configurations

#### 2. Theme Management (`ClientThemeManager`)

Singleton class that:
- Stores all registered client configurations
- Manages current active client selection
- Provides access to current client's theme data
- Handles initialization and client switching

#### 3. Build System Integration

**Android (`android/app/build.gradle`)**:
```gradle
productFlavors {
    tower {
        dimension "client"
        buildConfigField "String", "CLIENT_ID", '"81"'
        applicationIdSuffix ".tower"
        versionNameSuffix "-tower"
    }
    // ... other flavors
}
```

**Kotlin Bridge (`MainActivity.kt`)**:
```kotlin
MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "sacco_config")
    .setMethodCallHandler { call, result ->
        when (call.method) {
            "getClientId" -> result.success(BuildConfig.CLIENT_ID)
        }
    }
```

#### 4. Utility Classes

**`ThemeHelper`** (`lib/utils/theme_helper.dart`):
- Provides themed UI components
- Simplifies access to client colors and fonts
- Offers pre-styled widgets (AppBar, Buttons, Cards, etc.)

**`ColorPalette`** (`lib/utils/ColorPalette.dart`):
- Legacy color access (automatically uses client colors)
- Maintains backward compatibility

## Current Client Configurations

### Tower Sacco (Client ID: 81)
```dart
ClientConfig(
  clientId: '81',
  displayName: 'Tower Sacco',
  colors: ClientColorPalette(
    primary: Color(0xFF00A651),      // Green
    secondary: Color(0xFFEC008C),    // Pink
    // ... other colors
  ),
  logoAsset: 'assets/logos/logo.png',
  splashConfig: SplashScreenConfig(
    type: "image",
    assetPath: 'assets/images/splash.png',
    duration: Duration(seconds: 3),
  ),
)
```

### Mentor Cash (Client ID: 38)
```dart
ClientConfig(
  clientId: '38',
  displayName: 'Mentor Cash',
  colors: ClientColorPalette(
    primary: Color(0xFFF38B32),      // Orange
    secondary: Color(0xFF067C4B),    // Green
    // ... other colors
  ),
  logoAsset: 'assets/logos/38/logo.png',
  splashConfig: SplashScreenConfig(
    type: "image",
    assetPath: 'assets/images/38/splash.png',
    duration: Duration(seconds: 3),
    fallbackImagePath: 'assets/logos/38/logo.png',
  ),
)
```

### Amica Sacco (Client ID: 116)
```dart
ClientConfig(
  clientId: '116',
  displayName: 'Amica Sacco',
  colors: ClientColorPalette(
    primary: Color(0xFF2648B6),      // Blue
    secondary: Color(0xFF87CEFA),    // Light Blue
    // ... other colors
  ),
  logoAsset: 'assets/logos/116/logo.png',
  splashConfig: SplashScreenConfig(
    type: "image",
    assetPath: 'assets/images/116/splash.png',
    duration: Duration(seconds: 3),
    fallbackImagePath: 'assets/logos/116/logo.png',
  ),
)
```

### Magadi Sacco (Client ID: 120)
```dart
ClientConfig(
  clientId: '120',
  displayName: 'Magadi Sacco',
  colors: ClientColorPalette(
    primary: Color(0xFF234e87),      // Blue
    secondary: Color(0xFF9b3915),    // Red
    // ... other colors
  ),
  logoAsset: 'assets/logos/120/logo.png',
  splashConfig: SplashScreenConfig(
    type: "image",
    assetPath: 'assets/images/120/splash.png',
    duration: Duration(seconds: 3),
    fallbackImagePath: 'assets/logos/120/logo.png',
  ),
)
```

### M-BORESHA (Client ID: 93)
```dart
ClientConfig(
  clientId: '93',
  displayName: 'M-BORESHA',
  colors: ClientColorPalette(
    primary: Color(0xFF388515),      // Green
    secondary: Color(0xFFB1D848),    // Light Green
    // ... other colors
  ),
  logoAsset: 'assets/logos/93/logo.png',
  splashConfig: SplashScreenConfig(
    type: "image",
    assetPath: 'assets/images/93/splash.png',
    duration: Duration(seconds: 3),
    fallbackImagePath: 'assets/logos/93/logo.png',
  ),
)
```

### Ollin Sacco (Client ID: 54)
```dart
ClientConfig(
  clientId: '54',
  displayName: 'Ollin Sacco',
  colors: ClientColorPalette(
    primary: Color(0xFF00AEEF),      // Blue
    secondary: Color(0xFFF58220),    // Orange
    // ... other colors
  ),
  logoAsset: 'assets/logos/54/logo.png',
  splashConfig: SplashScreenConfig(
    type: "image",
    assetPath: 'assets/images/54/splash.png',
    duration: Duration(seconds: 3),
    fallbackImagePath: 'assets/logos/54/logo.png',
  ),
)
```

### Tangazoletu Sacco (Client ID: 999)
```dart
ClientConfig(
  clientId: '999',
  displayName: 'Tangazoletu Sacco',
  colors: ClientColorPalette(
    primary: Color(0xFF3987CA),      // Blue
    secondary: Color(0xFF8CC543),    // Green
    // ... other colors
  ),
  logoAsset: 'assets/logos/999/logo.png',
  splashConfig: SplashScreenConfig(
    type: "image",
    assetPath: 'assets/images/999/splash.png',
    duration: Duration(seconds: 3),
    fallbackImagePath: 'assets/logos/999/logo.png',
  ),
)
```

## Adding a New Client

### Step 1: Determine Client ID
Choose a unique client ID (e.g., "150" for a new Sacco).

### Step 2: Add Android Build Flavor

Edit `android/app/build.gradle` and add the new flavor:

```gradle
productFlavors {
    // ... existing flavors ...
    
    newSacco {
        dimension "client"
        buildConfigField "String", "CLIENT_ID", '"150"'
        applicationIdSuffix ".newsacco"
        versionNameSuffix "-newsacco"
    }
}
```

### Step 3: Create Asset Directories

Create the following directory structure:
```
assets/
├── images/150/
│   └── splash.png          # Client-specific splash screen
├── logos/150/
│   └── logo.png           # Client-specific logo
└── assets_config/
    └── 150.yaml           # Optional: Client-specific config
```

### Step 4: Create Android-specific Assets

Create the Android app icon and strings:
```
android/app/src/newSacco/
├── res/
│   ├── values/
│   │   └── strings.xml     # App name configuration
│   ├── mipmap-mdpi/
│   │   ├── ic_launcher.png
│   │   └── launcher_icon.png
│   ├── mipmap-hdpi/
│   │   ├── ic_launcher.png
│   │   └── launcher_icon.png
│   ├── mipmap-xhdpi/
│   │   ├── ic_launcher.png
│   │   └── launcher_icon.png
│   ├── mipmap-xxhdpi/
│   │   ├── ic_launcher.png
│   │   └── launcher_icon.png
│   └── mipmap-xxxhdpi/
│       ├── ic_launcher.png
│       └── launcher_icon.png
```

**strings.xml content:**
```xml
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Your Sacco Name</string>
</resources>
```

### Step 5: Update pubspec.yaml

Add the new asset paths:
```yaml
flutter:
  assets:
    # ... existing assets ...
    - assets/images/150/
    - assets/logos/150/
```

### Step 6: Register Client Configuration

In `lib/configuration/client_configurations.dart`, add the registration method:

```dart
// Add this method to the ClientConfigurations class
static void _registerNewSaccoClient(ClientThemeManager manager) {
  manager.registerClientConfig('150', ClientConfig(
    clientId: '150',
    displayName: 'New Sacco Name',
    colors: ClientColorPalette(
      primary: Color(0xFF123456),        // Choose primary color
      secondary: Color(0xFF654321),      // Choose secondary color
      background: Color(0xFFF5F5F5),
      surface: Color(0xFFFFFFFF),
      error: Color(0xFFD32F2F),
      success: Color(0xFF43A047),
      warning: Color(0xFFFFA000),
      info: Color(0xFF1976D2),
      textPrimary: Color(0xFF212121),
      textSecondary: Color(0xFF757575),
      textDisabled: Color(0xFFBDBDBD),
      modalBackground: Color(0xFF6B4779),
      unselectedNavItem: Color(0xFF6B4779),
    ),
    logoAsset: 'assets/logos/150/logo.png',
    fontFamily: 'Roboto', // or custom font
    splashConfig: SplashScreenConfig(
      type: "image",
      assetPath: 'assets/images/150/splash.png',
      duration: const Duration(seconds: 3),
      fallbackImagePath: 'assets/logos/150/logo.png',
    ),
  ));
}

// Update the registerAllClients method
static void registerAllClients() {
  final manager = ClientThemeManager();
  
  // ... existing registrations ...
  _registerNewSaccoClient(manager);  // Add this line
}
}

### Step 7: Create Assets

**Logo Requirements:**
- Format: PNG with transparent background
- Recommended size: 200x200px minimum
- Should work on both light and dark backgrounds

**Splash Screen Requirements:**
- Format: PNG
- Recommended size: 1080x1920px (9:16 aspect ratio)
- Should include logo if desired

**Android Icon Requirements:**
- Format: PNG with transparent background
- Size: 240x240px (all densities use same size in this project)
- Should be clear and recognizable at small sizes

### Step 8: Test the Configuration

```bash
# Build and test the new flavor
flutter run --flavor newSacco --dart-define=CLIENT_ID=150

# Build release APK
flutter build apk --flavor newSacco --dart-define=CLIENT_ID=150
```

## Building Different Flavors

### Command Line Methods

#### Development/Debug Builds
```bash
# Ollin Sacco
flutter run --flavor ollin --dart-define=CLIENT_ID=54

# Tower Sacco
flutter run --flavor tower --dart-define=CLIENT_ID=81

# Mentor Cash
flutter run --flavor mentor --dart-define=CLIENT_ID=38

# M-BORESHA
flutter run --flavor mboresha --dart-define=CLIENT_ID=93

# Amica Sacco
flutter run --flavor amica --dart-define=CLIENT_ID=116

# Magadi Sacco
flutter run --flavor magadi --dart-define=CLIENT_ID=120

# Tangazoletu Sacco
flutter run --flavor tangazoletu --dart-define=CLIENT_ID=999
```

#### Production/Release Builds
```bash
# Build Ollin Sacco APK
flutter build apk --flavor ollin --dart-define=CLIENT_ID=54

# Build Tower Sacco APK
flutter build apk --flavor tower --dart-define=CLIENT_ID=81

# Build App Bundle (for Play Store)
flutter build appbundle --flavor mentor --dart-define=CLIENT_ID=38

# Build M-BORESHA
flutter build apk --flavor mboresha --dart-define=CLIENT_ID=93

# Build iOS (requires macOS)
flutter build ios --flavor amica --dart-define=CLIENT_ID=116

# Build Tangazoletu Sacco
flutter build apk --flavor tangazoletu --dart-define=CLIENT_ID=999
```

### Android Studio/VS Code

1. **Android Studio:**
   - Go to **Run → Edit Configurations**
   - Set **Build flavor** to desired flavor (e.g., `mentor`)
   - Set **Additional run args** to `--dart-define=CLIENT_ID=38`
   - Click **Apply** and **OK**

2. **VS Code:**
   - Create `.vscode/launch.json`:
   ```json
   {
     "version": "0.2.0",
     "configurations": [
       {
         "name": "Tower Sacco",
         "request": "launch",
         "type": "dart",
         "program": "lib/main.dart",
         "args": ["--flavor", "tower", "--dart-define=CLIENT_ID=81"]
       },
       {
         "name": "Mentor Cash",
         "request": "launch",
         "type": "dart",
         "program": "lib/main.dart",
         "args": ["--flavor", "mentor", "--dart-define=CLIENT_ID=38"]
       },
       {
         "name": "M-BORESHA",
         "request": "launch",
         "type": "dart",
         "program": "lib/main.dart",
         "args": ["--flavor", "mboresha", "--dart-define=CLIENT_ID=93"]
       },
       {
         "name": "Tangazoletu Sacco",
         "request": "launch",
         "type": "dart",
         "program": "lib/main.dart",
         "args": ["--flavor", "tangazoletu", "--dart-define=CLIENT_ID=999"]
       }
     ]
   }
   ```

## Using Configurable Themes in Code

### Method 1: ThemeHelper (Recommended)

```dart
import '../utils/theme_helper.dart';

class MyScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Themed AppBar
      appBar: ThemeHelper.getThemedAppBar(title: 'My Screen'),
      
      body: Column(
        children: [
          // Themed Button
          ThemeHelper.getThemedButton(
            text: 'Submit',
            onPressed: () {},
          ),
          
          // Themed Card
          ThemeHelper.getThemedCard(
            child: Text('Card Content'),
          ),
          
          // Client Logo
          ThemeHelper.getClientLogo(width: 100, height: 50),
          
          // Themed Input Field
          TextField(
            decoration: ThemeHelper.getThemedInputDecoration(
              hintText: 'Enter text',
            ),
          ),
        ],
      ),
    );
  }
}
```

### Method 2: Direct ClientThemeManager Access

```dart
import '../configuration/client_config.dart';

class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colors = ClientThemeManager().colors;
    final config = ClientThemeManager().currentClientConfig;
    
    return Container(
      color: colors.primary,
      child: Text(
        config.displayName,
        style: TextStyle(
          color: colors.textPrimary,
          fontFamily: config.fontFamily,
        ),
      ),
    );
  }
}
```

### Method 3: Legacy ColorPalette (Backward Compatibility)

```dart
import '../utils/color_palette.dart';

// ColorPalette automatically uses current client colors
Container(
  color: ColorPalette.primary,
  child: Text(
    'Hello',
    style: TextStyle(color: ColorPalette.textPrimary),
  ),
)
```

## Asset Management

### Directory Structure
```
assets/
├── images/                    # Default images
│   ├── splash.png            # Default splash
│   ├── 38/                   # Mentor Cash specific
│   │   └── splash.png
│   ├── 93/                   # M-BORESHA specific
│   │   └── splash.png
│   ├── 116/                  # Amica Sacco specific
│   │   └── splash.png
│   └── 120/                  # Magadi Sacco specific
│       └── splash.png
├── logos/                     # Default logos
│   ├── logo.png              # Default logo
│   ├── 38/                   # Mentor Cash specific
│   │   └── logo.png
│   ├── 93/                   # M-BORESHA specific
│   │   └── logo.png
│   ├── 116/                  # Amica Sacco specific
│   │   └── logo.png
│   └── 120/                  # Magadi Sacco specific
│       └── logo.png
├── icons/                     # Shared icons
│   ├── airtel_money.png
│   ├── mastercard.png
│   ├── mpesa.png
│   ├── paypal.png
│   └── visa.png
└── assets_config/             # Optional config files
    ├── 38.yaml
    ├── 116.yaml
    └── 120.yaml
```

### Asset Loading Logic

The system automatically resolves assets based on the current client:

1. **Client-specific path**: `assets/logos/93/logo.png` (for M-BORESHA)
2. **Fallback to default**: `assets/logos/logo.png`

## Environment Configuration

### Priority Order

The system determines the client ID in this priority order:

1. **Build argument** (`--dart-define=CLIENT_ID=38`)
2. **Android BuildConfig** (from flavor)
3. **Environment file** (`assets/.env`)
4. **Default fallback** (999)

### Environment File (`assets/.env`)

```env
# Default client configuration
CLIENT_ID=81

# API Configuration
BASE_URL=https://api.towersacco.com
API_VERSION=v1

# Feature flags
ENABLE_BIOMETRIC_AUTH=true
ENABLE_DARK_MODE=true
```

## Testing and Debugging

### Debug Information

The app prints debug information during startup:

```
=== Starting app ===
CLIENT_ID from environment: 38
Environment loaded successfully
CLIENT_ID from .env: 81
Using CLIENT_ID from build argument: 38
Theme system initialized with client: 38
```

### Testing Different Configurations

1. **Verify Colors**: Check that UI elements use correct colors
2. **Check Assets**: Ensure logos and splash screens are client-specific
3. **Test App Name**: Verify app title shows correct client name
4. **Validate Functionality**: Ensure all features work across clients

### Common Debug Commands

```bash
# Clean build
flutter clean && flutter pub get

# Verbose build output
flutter run --flavor mentor --dart-define=CLIENT_ID=38 --verbose

# Check which assets are bundled
flutter build apk --flavor mboresha --dart-define=CLIENT_ID=93 --verbose
```

## Troubleshooting

### Common Issues

#### 1. Colors Not Changing
**Problem**: UI still shows default colors after switching clients.
**Solutions**:
- Ensure you're using `ColorPalette.primary` or `ThemeHelper.colors.primary`
- Avoid hardcoded colors: `Color(0xFF123456)`
- Verify ClientThemeManager is initialized in `main.dart`

#### 2. Assets Not Loading
**Problem**: Client-specific assets (logos, splash) not appearing.
**Solutions**:
- Check asset paths in `pubspec.yaml`
- Verify files exist in correct directories
- Ensure build flavor is set correctly
- Check file names match exactly (case-sensitive)

#### 3. Build Errors
**Problem**: Build fails with flavor-related errors.
**Solutions**:
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter build apk --flavor mentor --dart-define=CLIENT_ID=38
```

#### 4. Wrong Client ID
**Problem**: App shows wrong client configuration.
**Solutions**:
- Check build command includes correct `--dart-define=CLIENT_ID=XX`
- Verify Android flavor configuration in `build.gradle`
- Check `.env` file CLIENT_ID value
- Ensure no cached preferences override the client ID

## Best Practices

### 1. Color Selection
- **Contrast**: Ensure sufficient contrast between text and background colors
- **Accessibility**: Test with colorblind simulators
- **Brand Consistency**: Use official brand colors from client style guides
- **Readability**: Ensure text is readable on all background colors

### 2. Asset Guidelines
- **Logo**: Use vector formats when possible, PNG with transparent background
- **Splash Screen**: High resolution (at least 1080x1920), include brand elements
- **Icons**: Consistent style across all client-specific icons
- **File Naming**: Use consistent, descriptive names

### 3. Code Organization
- **Separation of Concerns**: Keep client-specific logic in configuration files
- **Reusability**: Use ThemeHelper for common UI components
- **Maintainability**: Document any client-specific business logic
- **Testing**: Test each client configuration thoroughly

## Essential Files Reference

### Key Configuration Files
- `lib/configuration/client_config.dart` - Data models and ClientThemeManager
- `lib/configuration/client_configurations.dart` - Client registrations
- `lib/main.dart` - App initialization and client selection logic
- `lib/utils/theme_helper.dart` - UI utility functions
- `lib/utils/ColorPalette.dart` - Legacy color access
- `android/app/build.gradle` - Android build flavors
- `android/app/src/main/kotlin/.../MainActivity.kt` - Native bridge
- `pubspec.yaml` - Asset declarations
- `assets/.env` - Environment configuration

### Quick Commands Reference
```bash
# Run specific client in debug
flutter run --flavor mboresha --dart-define=CLIENT_ID=93

# Build release APK
flutter build apk --flavor mboresha --dart-define=CLIENT_ID=93

# Clean build
flutter clean && flutter pub get

# Check available flavors
grep -A 10 "productFlavors" android/app/build.gradle
```

### Color Usage Examples
```dart
// Recommended approaches
ThemeHelper.colors.primary
ClientThemeManager().colors.primary

// Legacy (still works)
ColorPalette.primary

// Avoid (hardcoded)
Color(0xFF123456)
```

---

**Last Updated**: July 2025  
**Version**: 2.0  
**Maintainer**: Development Team

For questions or issues with this configuration system, please contact the development team or create an issue in the project repository. 