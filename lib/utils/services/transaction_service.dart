import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'api_service.dart';
import 'cryptographer.dart';
import 'shared_preferences_helper.dart';
import 'api_endpoints.dart';
import 'token_refresh_service.dart';
import '../../transactions/screens/transaction_receipt_screen.dart';

import '../app_logger.dart';

typedef OtpInputCallback = Future<String?> Function();
typedef TransactionStatusCallback = void Function(bool isLoading);
typedef TransactionCompletedCallback = void Function(
    Map<String, dynamic> response, bool success);

class TransactionService {
  final ApiService _apiService = ApiService();
  final AesEncryption _aesEncryption = AesEncryption();

  // Process any transaction type including deposit
  Future<void> processTransaction({
    required String serviceCode,
    required Map<String, dynamic> formData,
    required BuildContext context,
    required TransactionStatusCallback statusCallback,
    required TransactionCompletedCallback completedCallback,
    required OtpInputCallback otpCallback,
    String? balanceEnquiryType,
  }) async {
    statusCallback(true); // Start loading

    try {
      // Generate payload
      Map<String, dynamic> payload = await _generateTransactionPayload(
        serviceCode: serviceCode,
        formData: formData,
        balanceEnquiryType: balanceEnquiryType,
      );

      // Execute transaction
      var response = await _apiService.postRequest(
        ApiEndpoints.transaction,
        payload,
        context: context,
        isTransactional: true,
      );

      AppLogger.info('=== RAW TRANSACTION RESPONSE ===');
      AppLogger.info('Response: $response');

      // Safety check for response
      if (response == null || !response.containsKey('hashedBody')) {
        throw Exception("Invalid response from server");
      }

      // Get secret key for decryption
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? secretKey = prefs.getString('_tajemnica');

      if (secretKey == null) {
        throw Exception("Secret key not found");
      }

      // Safely decrypt response
      String decryptedResponse;
      try {
        decryptedResponse = _aesEncryption.decryptWithBase64Key(
            response['hashedBody'], secretKey);

        if (decryptedResponse.isEmpty) {
          throw Exception("Empty decrypted response");
        }
      } catch (e) {
        AppLogger.info('Decryption error: $e');
        throw Exception("Failed to decrypt response: $e");
      }

      // Parse the decrypted response
      final Map<String, dynamic> decryptedResponseMap =
          json.decode(decryptedResponse.trim());

      AppLogger.info('=== TRANSACTION RESPONSE DEBUG ===');
      AppLogger.info('Response header: ${decryptedResponseMap['header']}');
      AppLogger.info(
          'Response code (sc): ${decryptedResponseMap['header']?['sc']}');
      AppLogger.info(
          'Root response code: ${decryptedResponseMap['responseCode']}');
      AppLogger.info(
          'Response message: ${decryptedResponseMap['responseData']?['message']}');
      AppLogger.info('Full response: $decryptedResponseMap');

      // Check if OTP is required - the backend sends responseCode: 22 and message: "OTP SENT"
      String responseCode =
          decryptedResponseMap['responseCode']?.toString() ?? '';
      String headerResponseCode =
          decryptedResponseMap['header']?['sc']?.toString() ?? '';
      String message =
          decryptedResponseMap['responseData']?['message']?.toString() ?? '';

      // OTP required detection - responseCode 22 means OTP sent
      // Also force OTP for inter-account transfers regardless of backend response
      bool isInterAccountTransfer =
          _isInterAccountTransfer(serviceCode, formData);
      bool otpRequired = responseCode == '22' ||
          message.toLowerCase().contains('otp sent') ||
          message.toLowerCase().contains('otp') ||
          headerResponseCode == '01' ||
          headerResponseCode == '02' ||
          headerResponseCode == '03' ||
          headerResponseCode == 'OTP' ||
          headerResponseCode == 'PIN' ||
          isInterAccountTransfer; // Force OTP for inter-account transfers

      if (otpRequired) {
        AppLogger.info('=== OTP REQUIRED DETECTED ===');
        AppLogger.info('Response code: $responseCode');
        AppLogger.info('Is inter-account transfer: $isInterAccountTransfer');
        AppLogger.info('Calling OTP callback...');

        // Handle OTP flow - pass original form data to regenerate payload with fresh encryption
        await _handleOtpFlow(
          payload: payload, // Pass current payload for reference
          secretKey: secretKey,
          context: context,
          statusCallback: statusCallback,
          completedCallback: completedCallback,
          otpCallback: otpCallback,
          balanceEnquiryType: balanceEnquiryType,
          serviceCode: serviceCode,
          formData: formData,
        );
        return;
      }

      // Check if transaction was successful using safe header access
      bool isSuccess = _isTransactionSuccessful(decryptedResponseMap);

      if (isSuccess) {
        Map<String, dynamic> receiptData;
        if (serviceCode == 'MS') {
          final msm =
              decryptedResponseMap['responseData']?['msm'] as String? ?? '';
          final transactions =
              msm.split('|').where((s) => s.trim().isNotEmpty).toList();
          receiptData = {
            for (var i = 0; i < transactions.length; i++)
              'Transaction ${i + 1}': transactions[i].trim()
          };
        } else if (serviceCode == 'AS') {
          receiptData = {
            'Status': decryptedResponseMap['header']?['st'] ?? 'Success',
            'Details':
                'Your full statement has been sent to your registered email address.'
          };
        } else {
          receiptData = decryptedResponseMap['responseData'] ?? {};
        }

        statusCallback(false);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionReceiptScreen(
              receiptData: receiptData,
              serviceCode: serviceCode,
            ),
          ),
        );
      } else {
        // Transaction completed (success or failure)
        statusCallback(false); // Stop loading
        completedCallback(decryptedResponseMap, isSuccess);
      }
    } catch (e) {
      AppLogger.info('Transaction error: $e');
      statusCallback(false); // Stop loading
      completedCallback({'error': e.toString()}, false);
    }
  }

  // Generate appropriate payload for the transaction type
  Future<Map<String, dynamic>> _generateTransactionPayload({
    required String serviceCode,
    required Map<String, dynamic> formData,
    String? balanceEnquiryType,
  }) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? secretKey = prefs.getString('_tajemnica');
      String phoneNumber = prefs.getString('msisdn') ?? '';
      var clientId = await SharedPreferencesHelper.getClientId();
      var spCustId = await SharedPreferencesHelper.getCustId();
      String? imei = await _apiService.getDeviceId();

      if (secretKey == null || secretKey.isEmpty) {
        throw Exception("Secret key not found or empty");
      }

      if (phoneNumber.isEmpty) {
        throw Exception("Phone number not found");
      }

      // Test encryption validity
      try {
        _aesEncryption.encryptWithBase64Key("test", secretKey);
      } catch (e) {
        AppLogger.info('Secret key appears to be invalid: $e');
        throw Exception("Invalid encryption key. Please log in again.");
      }

      // Build request header
      Map<String, dynamic> header = {
        "categoryName": "Spotcash_App",
        "clientId": clientId,
        "msisdn": _safeEncrypt(phoneNumber, secretKey),
        "imei": imei,
      };

      // Fields that need encryption
      Set<String> encryptedFields = {
        'accNo',
        'cmp',
        'pin',
        'custid',
        'description',
        'otp',
        'transPin',
        'otherId',
        'otherAccNo'
      };

      // Build request data
      Map<String, dynamic> requestData = {};

      // Add all form data to request
      formData.forEach((key, value) {
        if (value != null && value.toString().isNotEmpty) {
          // Handle date fields - format them properly
          if (value is DateTime) {
            if (key == 'startDate' || key == 'endDate') {
              // Format dates as MM-dd-yyyy for the API
              String formattedDate = DateFormat("MM-dd-yyyy").format(value);
              requestData[key] = formattedDate;
            } else {
              requestData[key] = value.toIso8601String();
            }
          }
          // Handle encrypted fields
          else if (encryptedFields.contains(key)) {
            requestData[key] = _safeEncrypt(value.toString(), secretKey);
          }
          // Handle regular fields
          else {
            requestData[key] = value;
          }
        }
      });

      // Add customer ID if not already in formData
      if (!requestData.containsKey('custid') && spCustId != null) {
        requestData["custid"] = _safeEncrypt(spCustId, secretKey);
      }

      // Add customer type
      requestData["customerType"] = "CUSTOMER";

      // Set transaction type based on service code if not already in formData
      if (!requestData.containsKey('txnType')) {
        requestData["txnType"] =
            _getTransactionType(serviceCode, formData, balanceEnquiryType);
      }

      // Special handling for different service types
      if (serviceCode == 'AS') {
        // Email statement service specific handling
        if (formData.containsKey('emailAddress')) {
          String emailValue = formData['emailAddress']?.toString() ?? '';
          if (emailValue.isNotEmpty) {
            requestData['emailAddress'] = emailValue; // Keep email unencrypted
          }
        }

        if (!requestData.containsKey('amt')) {
          requestData['amt'] = '0';
        }

        if (!requestData.containsKey('description')) {
          requestData['description'] =
              _safeEncrypt('Full Statement', secretKey);
        }
      }
      // Enhanced IATOT handling
      else if (_isInterAccountTransfer(serviceCode, formData)) {
        _handleInterAccountTransferFields(requestData, formData, secretKey);
      }
      // For deposit transactions, handle specific requirements
      else if (_isDepositTransaction(serviceCode)) {
        _handleDepositSpecificFields(requestData, formData, secretKey);
      }
      // For balance enquiry transactions, set amount to 0 if not provided
      else if (_isBalanceEnquiry(serviceCode, balanceEnquiryType) &&
          !requestData.containsKey('amt')) {
        requestData["amt"] = "0";
      }

      // Initial empty OTP if needed
      if (!requestData.containsKey('otp')) {
        requestData["otp"] = _safeEncrypt("", secretKey);
      }

      AppLogger.info("=== GENERATED TRANSACTION PAYLOAD ===");
      AppLogger.info("Service Code: $serviceCode");
      AppLogger.info("Transaction Type: ${requestData['txnType']}");
      AppLogger.info("Request Data Keys: ${requestData.keys.toList()}");

      return {"header": header, "requestData": requestData};
    } catch (e) {
      AppLogger.error('generating transaction payload: $e');
      throw Exception('Error generating transaction payload: $e');
    }
  }

  bool _isInterAccountTransfer(
      String serviceCode, Map<String, dynamic> formData) {
    return serviceCode == 'IATOT' ||
        serviceCode == 'IAT' ||
        serviceCode == 'IATOW' ||
        (formData.containsKey('txnType') &&
            (formData['txnType'] == 'IATOT' ||
                formData['txnType'] == 'IAT' ||
                formData['txnType'] == 'IATOW'));
  }

  // Handle inter-account transfer specific fields
  void _handleInterAccountTransferFields(
    Map<String, dynamic> requestData,
    Map<String, dynamic> formData,
    String secretKey,
  ) {
    AppLogger.info("=== HANDLING INTER-ACCOUNT TRANSFER FIELDS ===");

    // Set appropriate description
    if (!requestData.containsKey('description')) {
      String description = '';
      String? txnType = formData['txnType'];
      if (txnType == 'IAT') {
        description = 'Transfer to Own Account';
      } else if (txnType == 'IATOT') {
        description = 'Transfer to Other Account';
      } else {
        description = 'Inter-Account Transfer';
      }
      requestData['description'] = _safeEncrypt(description, secretKey);
    }

    // Ensure we have the destination account
    if (formData.containsKey('otherAccNo') &&
        !requestData.containsKey('otherAccNo')) {
      requestData['otherAccNo'] =
          _safeEncrypt(formData['otherAccNo'].toString(), secretKey);
    }

    // Use txnType directly from form data
    if (formData.containsKey('txnType')) {
      requestData['txnType'] = formData['txnType'];
    } else {
      requestData['txnType'] = 'IAT'; // Default to own account transfer
    }

    AppLogger.info(
        "Inter-account transfer - Final txnType: ${requestData['txnType']}");
    AppLogger.info("Destination account: ${formData['otherAccNo']}");
    AppLogger.info(
        "Destination account encrypted: ${requestData.containsKey('otherAccNo')}");
  }

  // Handle deposit-specific field processing
  void _handleDepositSpecificFields(
    Map<String, dynamic> requestData,
    Map<String, dynamic> formData,
    String secretKey,
  ) {
    // Ensure description is empty for deposit transactions (as per original implementation)
    if (!requestData.containsKey('description')) {
      requestData['description'] = _safeEncrypt('', secretKey);
    }

    // For deposit transactions, the transaction type should be the service code from txnType field
    if (formData.containsKey('txnType') && requestData.containsKey('txnType')) {
      // txnType field contains the actual service code (STKSAVINGS, STKSHARES, etc.)
      requestData['txnType'] = formData['txnType'];
    }

    AppLogger.info(
        "Deposit transaction - Final txnType: ${requestData['txnType']}");
  }

  // Check if this is a deposit transaction
  bool _isDepositTransaction(String serviceCode) {
    List<String> depositServiceCodes = [
      'STK',
      'STKLOANS',
      'STKSAVINGS',
      'STKSHARES',
      'DEP'
    ];
    return depositServiceCodes.contains(serviceCode);
  }

  // Helper method for safe encryption
  String _safeEncrypt(String value, String key) {
    try {
      if (value.isEmpty) {
        return "";
      }

      if (key.isEmpty) {
        throw Exception("Empty encryption key");
      }

      return _aesEncryption.encryptWithBase64Key(value, key);
    } catch (e) {
      AppLogger.info('Safe encryption error for value: $e');
      return "ENCRYPTION_FAILED";
    }
  }

  // Determine transaction type based on service code and form data
  String _getTransactionType(String serviceCode, Map<String, dynamic> formData,
      [String? balanceEnquiryType]) {
    // Use explicit transaction type from form data if available (important for all transactions)
    if (formData.containsKey('txnType')) {
      AppLogger.info(
          "Using explicit txnType from form data: ${formData['txnType']}");
      return formData['txnType'];
    }

    // For balance enquiry, use the child service code if provided
    if (balanceEnquiryType != null && serviceCode == 'BE') {
      return _mapBalanceEnquiryType(balanceEnquiryType);
    }

    // Enhanced IATOT handling
    if (serviceCode == 'IATOT') {
      // Determine IAT sub-type based on form data
      if (formData.containsKey('recipientType')) {
        String recipientType = formData['recipientType'];
        return recipientType == 'other' ? 'IAT' : 'IATOT';
      }
      return 'IATOT'; // Default to own account transfer
    }

    // Map service code to transaction type
    switch (serviceCode) {
      case 'WD':
        return formData['description'] != null ? 'WDO' : 'WD';
      case 'DP':
        return 'DP';
      case 'IAT':
      case 'IATOT':
      case 'IATOW':
        return serviceCode;
      case 'MS':
        return 'MS';
      case 'AS':
        return 'AS';
      case 'LA':
        return 'LA';
      case 'RL':
        return 'RL';
      case 'AT':
        return 'AT';
      case 'ATO':
        return 'ATO';
      case 'F2B':
        return 'F2B';
      case 'PU':
        return 'PU';
      case 'BE':
        return 'BE';
      // Balance enquiry transaction types
      case 'LB':
        return 'LB'; // Loan Balance
      case 'NWDB':
        return 'NWDB'; // NWD Balance
      case 'SCBE':
        return 'SCBE'; // Share Capital Balance Enquiry
      case 'SBE':
        return 'SBE'; // Savings Balance Enquiry
      // Deposit transaction types - return the service code as transaction type
      case 'STKSAVINGS':
        return 'STKSAVINGS';
      case 'STKSHARES':
        return 'STKSHARES';
      case 'STKLOANS':
        return 'STKLOANS';
      case 'STK':
        return 'STK';
      // Utility bill transaction types
      case 'DSTV':
        return 'DSTV';
      case 'ZUKU':
        return 'ZUKU';
      case 'STAR':
        return 'STAR';
      case 'KPLC':
        return 'KPLC';
      case 'NWTR':
        return 'NWTR';
      default:
        return serviceCode;
    }
  }

  // Map balance enquiry type to transaction type
  String _mapBalanceEnquiryType(String balanceEnquiryType) {
    switch (balanceEnquiryType) {
      case 'LB':
        return 'LB'; // Loan Balance
      case 'NWDB':
        return 'NWDB'; // NWD Balance
      case 'SCBE':
        return 'SCBE'; // Share Capital Balance Enquiry
      case 'SBE':
        return 'SBE'; // Savings Balance Enquiry
      default:
        return balanceEnquiryType;
    }
  }

  // Helper method to check if service code is for balance enquiry
  bool _isBalanceEnquiry(String serviceCode, [String? balanceEnquiryType]) {
    if (balanceEnquiryType != null) {
      return ['LB', 'NWDB', 'SCBE', 'SBE'].contains(balanceEnquiryType);
    }
    return ['LB', 'NWDB', 'SCBE', 'SBE', 'BE'].contains(serviceCode);
  }

  // Safe method to check transaction success
  bool _isTransactionSuccessful(Map<String, dynamic> responseData) {
    // Use existing response structure patterns
    if (responseData.containsKey('header') && responseData['header'] != null) {
      Map<String, dynamic> header = responseData['header'];
      return header.containsKey('sc') && header['sc'] == '00';
    }

    // Fallback check for response code
    if (responseData.containsKey('responseCode')) {
      return responseData['responseCode'] == '00';
    }

    return false;
  }

  // Build request data for OTP flow

  // Handle OTP flow
  Future<void> _handleOtpFlow({
    required Map<String, dynamic> payload,
    required String secretKey,
    required BuildContext context,
    required TransactionStatusCallback statusCallback,
    required TransactionCompletedCallback completedCallback,
    required OtpInputCallback otpCallback,
    String? balanceEnquiryType,
    String? serviceCode,
    Map<String, dynamic>? formData,
  }) async {
    // Request OTP input from user
    String? otp = await otpCallback();

    if (otp == null || otp.isEmpty) {
      statusCallback(false);
      completedCallback({'error': 'OTP cancelled'}, false);
      return;
    }

    statusCallback(true); // Show loading during OTP verification

    try {
      AppLogger.info('=== PROCESSING OTP TRANSACTION ===');
      AppLogger.info('OTP provided: ${otp.length} characters');

      // Refresh token to get fresh secret key before sending OTP transaction
      await TokenRefreshService.instance.forceRefreshToken();
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? freshSecretKey = prefs.getString('_tajemnica');

      if (freshSecretKey == null) {
        throw Exception("Secret key not found after token refresh");
      }

      AppLogger.info('=== REGENERATING PAYLOAD WITH FRESH ENCRYPTION ===');

      // Regenerate the entire payload with fresh secret key and OTP
      Map<String, dynamic> freshPayload;
      if (serviceCode != null && formData != null) {
        // Add OTP to form data
        Map<String, dynamic> formDataWithOtp = Map.from(formData);
        formDataWithOtp['otp'] = otp; // Add unencrypted OTP to form data

        // Generate fresh payload using the existing method
        freshPayload = await _generateTransactionPayload(
          serviceCode: serviceCode,
          formData: formDataWithOtp,
          balanceEnquiryType: balanceEnquiryType,
        );
      } else {
        // Fallback: update existing payload with OTP
        freshPayload = Map.from(payload);
        if (freshPayload.containsKey('requestData')) {
          freshPayload['requestData']['otp'] =
              _safeEncrypt(otp, freshSecretKey);
        }

        // Re-encrypt header fields with fresh secret key
        String phoneNumber = prefs.getString('msisdn') ?? '';
        if (freshPayload['header'].containsKey('msisdn')) {
          freshPayload['header']['msisdn'] =
              _safeEncrypt(phoneNumber, freshSecretKey);
        }
      }

      // Submit with OTP using fresh payload
      var otpResponse = await _apiService.postRequest(
        ApiEndpoints.transaction,
        freshPayload,
        context: context,
        isTransactional: true,
      );

      // Safety check for response
      if (otpResponse == null || !otpResponse.containsKey('hashedBody')) {
        throw Exception("Invalid OTP response from server");
      }

      String decryptedOtpResponse;
      try {
        decryptedOtpResponse = _aesEncryption.decryptWithBase64Key(
            otpResponse['hashedBody'], freshSecretKey);

        if (decryptedOtpResponse.isEmpty) {
          throw Exception("Empty decrypted OTP response");
        }
      } catch (e) {
        AppLogger.info('OTP Decryption error: $e');
        throw Exception("Failed to decrypt OTP response: $e");
      }

      Map<String, dynamic> otpResponseData;
      try {
        otpResponseData = json.decode(decryptedOtpResponse);
      } catch (e) {
        AppLogger.info('OTP JSON parsing error: $e');
        throw Exception("Failed to parse OTP response: $e");
      }

      statusCallback(false); // Stop loading

      bool isSuccess = _isTransactionSuccessful(otpResponseData);
      completedCallback(otpResponseData, isSuccess);
    } catch (e) {
      statusCallback(false);
      completedCallback({'error': 'Error processing OTP: $e'}, false);
    }
  }

  // Account filtering methods
  static List<Map<String, dynamic>> filterAccounts({
    required List<dynamic> accounts,
    required String serviceCode,
  }) {
    if (accounts.isEmpty) return [];

    // Convert to list of maps for consistent handling
    List<Map<String, dynamic>> accountsList = accounts
        .where((account) => account != null)
        .map((account) => account is Map<String, dynamic>
            ? account
            : Map<String, dynamic>.from(account))
        .toList();

    // Apply filters based on service code
    switch (serviceCode) {
      case 'WD': // Withdrawal
        return accountsList
            .where((account) =>
                account['isSavingsAccount'] == 'Yes' &&
                account['canWithdraw'] == 'Yes')
            .toList();

      case 'DP': // Deposit
      case 'STKSAVINGS': // Savings Deposit
      case 'STKSHARES': // Shares Deposit
      case 'STKLOANS': // Loan Repayment
      case 'STK': // Investment Deposit
        return _filterDepositAccounts(accountsList, serviceCode);

      case 'IATOT': // Inter-Account Transfer
      case 'IAT': // Inter-Account Transfer to Other
      case 'IATOW': // Inter-Account Transfer to Own
        return accountsList
            .where((account) =>
                (account['isSavingsAccount'] == 'Yes' ||
                    account['isLoanAccount'] == 'Yes' ||
                    account['isNWD'] == 'Yes') &&
                account['canWithdraw'] == 'Yes')
            .toList();

      default:
        return accountsList;
    }
  }

  // Filter accounts specifically for deposit transactions
  static List<Map<String, dynamic>> _filterDepositAccounts(
      List<Map<String, dynamic>> accounts, String serviceCode) {
    switch (serviceCode) {
      case 'STKSAVINGS':
        return accounts
            .where((account) =>
                account['isSavingsAccount'] == 'Yes' &&
                account['canDeposit'] == 'Yes')
            .toList();

      case 'STKSHARES':
        return accounts
            .where((account) =>
                account['isShareCapital'] == 'Yes' &&
                account['canDeposit'] == 'Yes')
            .toList();

      case 'STKLOANS':
        return accounts
            .where((account) =>
                account['isLoanAccount'] == 'Yes' &&
                account['canDeposit'] == 'Yes')
            .toList();

      case 'STK':
        return accounts
            .where((account) =>
                account['isNWD'] == 'Yes' && account['canDeposit'] == 'Yes')
            .toList();

      default:
        return accounts
            .where((account) => account['canDeposit'] == 'Yes')
            .toList();
    }
  }

  // Format values for display
  static String formatFieldValue(String key, dynamic value, String fieldType) {
    // Format currency values
    if (key == 'amt' || key.toLowerCase().contains('amount')) {
      return 'KES ${value.toString()}';
    }

    // Mask password/pin values
    if (fieldType == 'password' || key == 'cmp' || key == 'pin') {
      return '●●●●';
    }

    // Format account information
    if ((key == 'accNo' || key == 'otherAccNo') &&
        value.toString().contains(' - ')) {
      return value
          .toString(); // Already formatted as "Account Name - Account Number"
    }

    // Format recipient type
    if (key == 'recipientType') {
      return value == 'own' ? 'My Account' : 'Other Account';
    }

    return value.toString();
  }
}
