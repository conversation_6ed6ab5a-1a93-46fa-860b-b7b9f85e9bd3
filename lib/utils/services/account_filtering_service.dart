import '../app_logger.dart';

class AccountFilterService {
  // Standard mapping for account types based on official configuration
  static const Map<String, Map<String, dynamic>> accountTypeMapping = {
    '1': {'isLoanAccount': 'Yes'}, // loanAccounts
    '2': {'isNWD': 'Yes'}, // nwdAccounts
    '3': {'isSavingsAccount': 'Yes'}, // savingsAccounts
    '4': {'isShareCapital': 'Yes'}, // shareAccounts
  };

  // Standard mapping for filter types based on official configuration
  static const Map<String, Map<String, dynamic>> filterTypeMapping = {
    '1': {'canWithdraw': 'Yes'}, // Can withdraw
    '2': {'canDeposit': 'Yes'}, // Can deposit
    '3': {'isLoanAccount': 'Yes'}, // Loan accounts only
    '4': {'isNWD': 'Yes'}, // NWD accounts only
    '5': {'isShareCapital': 'Yes'}, // Share capital accounts only
    '6': {'isSavingsAccount': 'Yes'}, // Savings accounts only
    '7': {'canWithdraw': 'No'}, // Cannot withdraw
  };

  /// Parse filter criteria from defaultValues array
  /// Example: ["accountType=3", "filterType=1", "canDeposit=Yes", "isSavingsAccount=true"]
  static Map<String, String> parseFilterCriteria(List<String>? defaultValues) {
    Map<String, String> criteria = {};

    if (defaultValues == null || defaultValues.isEmpty) {
      return criteria;
    }

    for (String filter in defaultValues) {
      if (filter.contains('=')) {
        List<String> parts = filter.split('=');
        if (parts.length == 2) {
          String key = parts[0].trim();
          String value = parts[1].trim();
          criteria[key] = value;
        }
      }
    }

    return criteria;
  }

  /// Convert filter criteria to account property filters
  static Map<String, dynamic> buildAccountFilters(
      Map<String, String> criteria) {
    Map<String, dynamic> filters = {};

    criteria.forEach((filterKey, filterValue) {
      switch (filterKey) {
        case 'accountType':
          // Map account type to properties
          if (accountTypeMapping.containsKey(filterValue)) {
            filters.addAll(accountTypeMapping[filterValue]!);
          }
          break;

        case 'filterType':
          // Map filter type to properties
          if (filterTypeMapping.containsKey(filterValue)) {
            filters.addAll(filterTypeMapping[filterValue]!);
          }
          break;

        case 'minBalance':
          // Handle minimum balance
          filters['minBalance'] = double.tryParse(filterValue) ?? 0.0;
          break;

        case 'maxBalance':
          // Handle maximum balance
          filters['maxBalance'] = double.tryParse(filterValue) ?? 0.0;
          break;

        case 'minWithdrawable':
          // Handle minimum withdrawable amount
          filters['minWithdrawable'] = double.tryParse(filterValue) ?? 0.0;
          break;

        case 'maxDeposit':
          // Handle maximum deposit amount
          filters['maxDeposit'] = double.tryParse(filterValue) ?? 0.0;
          break;

        default:
          // Handle direct property matching with normalization
          filters[filterKey] = _normalizeFilterValue(filterValue);
          break;
      }
    });

    return filters;
  }

  /// Normalize filter values to handle various formats
  static dynamic _normalizeFilterValue(String value) {
    String normalizedValue = value.trim().toLowerCase();

    switch (normalizedValue) {
      case 'yes':
      case 'true':
      case '1':
        return 'Yes'; // Standardize to 'Yes' for API consistency
      case 'no':
      case 'false':
      case '0':
        return 'No'; // Standardize to 'No' for API consistency
      default:
        return value; // Keep original value for other cases
    }
  }

  /// Apply filters to a list of accounts
  static List<Map<String, dynamic>> filterAccounts(
    List<dynamic> accounts,
    Map<String, dynamic> filters,
  ) {
    if (accounts.isEmpty || filters.isEmpty) {
      return accounts.cast<Map<String, dynamic>>();
    }

    return accounts
        .where((account) {
          if (account == null || account is! Map) return false;

          Map<String, dynamic> accountMap = Map<String, dynamic>.from(account);

          // Check each filter condition
          for (var filterEntry in filters.entries) {
            String filterKey = filterEntry.key;
            dynamic filterValue = filterEntry.value;

            if (!_matchesFilter(accountMap, filterKey, filterValue)) {
              return false; // Account doesn't match this filter
            }
          }

          return true; // Account matches all filters
        })
        .cast<Map<String, dynamic>>()
        .toList();
  }

  /// Check if an account matches a specific filter condition
  static bool _matchesFilter(
      Map<String, dynamic> account, String filterKey, dynamic filterValue) {
    switch (filterKey) {
      case 'minBalance':
        double? balance = _getNumericValue(account['balance']);
        double minBalance = filterValue is double ? filterValue : 0.0;
        return balance != null && balance >= minBalance;

      case 'maxBalance':
        double? balance = _getNumericValue(account['balance']);
        double maxBalance =
            filterValue is double ? filterValue : double.infinity;
        return balance != null && balance <= maxBalance;

      case 'minWithdrawable':
        double? withdrawable = _getNumericValue(
            account['withdrawableBalance'] ?? account['balance']);
        double minWithdrawable = filterValue is double ? filterValue : 0.0;
        return withdrawable != null && withdrawable >= minWithdrawable;

      case 'maxDeposit':
        // For deposit limits, we might need to check account-specific limits
        // For now, assume all accounts can accept the deposit
        return true;

      default:
        // Direct property matching
        dynamic accountValue = account[filterKey];

        // Handle string comparisons (case-insensitive)
        if (accountValue is String && filterValue is String) {
          return accountValue.toLowerCase() == filterValue.toLowerCase();
        }

        // Handle exact matches
        return accountValue == filterValue;
    }
  }

  /// Get numeric value from various formats
  static double? _getNumericValue(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      // Handle negative values and clean the string
      String cleanValue = value.replaceAll(RegExp(r'[^0-9.-]'), '');
      return double.tryParse(cleanValue);
    }
    return null;
  }

  /// Filter accounts based on journey field configuration
  static List<Map<String, dynamic>> filterAccountsForField(
    List<dynamic> allAccounts,
    Map<String, dynamic> fieldConfig,
  ) {
    // Extract configuration
    List<String>? defaultValues = fieldConfig['defaultValues']?.cast<String>();
    String? filterExpression = fieldConfig['filterExpression'];
    String? responseType = fieldConfig['responseType'];

    AppLogger.info("=== FILTERING ACCOUNTS FOR FIELD ===");
    AppLogger.info("Field config: $fieldConfig");
    AppLogger.info("Default values: $defaultValues");
    AppLogger.info("Filter expression: $filterExpression");
    AppLogger.info("Response type: $responseType");
    AppLogger.info("Total accounts: ${allAccounts.length}");

    // Start with all accounts
    List<Map<String, dynamic>> filteredAccounts =
        allAccounts.cast<Map<String, dynamic>>();

    // Apply default value filters if present
    if (defaultValues != null && defaultValues.isNotEmpty) {
      Map<String, String> criteria = parseFilterCriteria(defaultValues);
      AppLogger.info("Parsed criteria: $criteria");

      if (criteria.isNotEmpty) {
        Map<String, dynamic> filters = buildAccountFilters(criteria);
        AppLogger.info("Built filters: $filters");
        filteredAccounts = filterAccounts(filteredAccounts, filters);
        AppLogger.info(
            "After default filters: ${filteredAccounts.length} accounts");
      }
    }

    // Apply filter expression (e.g., exclude specific accounts)
    if (filterExpression != null && filterExpression.isNotEmpty) {
      filteredAccounts = _applyFilterExpression(
          filteredAccounts, filterExpression, fieldConfig);
      AppLogger.info(
          "After filter expression: ${filteredAccounts.length} accounts");
    }

    // Debug: Print details of filtered accounts
    AppLogger.info("=== FILTERED ACCOUNTS DETAILS ===");
    for (int i = 0; i < filteredAccounts.length && i < 3; i++) {
      var account = filteredAccounts[i];
      AppLogger.info(
          "Account ${i + 1}: ${account['accountName']} - ${account['accountNo']}");
    }

    return filteredAccounts;
  }

  static List<Map<String, dynamic>> _applyFilterExpression(
    List<Map<String, dynamic>> accounts,
    String filterExpression,
    Map<String, dynamic> fieldConfig,
  ) {
    AppLogger.info("=== APPLYING FILTER EXPRESSION: $filterExpression ===");

    // Handle exclude expressions
    if (filterExpression.startsWith('exclude:model.')) {
      String fieldName = filterExpression.substring('exclude:model.'.length);

      // Get the current form data to find the value to exclude
      dynamic excludeValue = fieldConfig['currentFormData']?[fieldName];

      if (excludeValue != null) {
        AppLogger.info("Excluding accounts with $fieldName = $excludeValue");

        return accounts.where((account) {
          String? accountNo = account['accountNo']?.toString();
          return accountNo != excludeValue.toString();
        }).toList();
      }
    }

    return accounts;
  }

  /// Create options list for dropdown from filtered accounts
  static List<Map<String, dynamic>> createAccountOptions(
      List<Map<String, dynamic>> filteredAccounts,
      {bool showBalance = true}) {
    return filteredAccounts.map((account) {
      String accountNo = account['accountNo']?.toString() ?? '';
      String accountName = account['accountName']?.toString() ?? '';
      double balance = _getNumericValue(account['balance']) ?? 0.0;

      String displayName =
          accountName.isNotEmpty ? '$accountName - $accountNo' : accountNo;
      String displayLabel = displayName;

      if (showBalance && balance > 0) {
        displayLabel = '$displayName (KES ${balance.toStringAsFixed(2)})';
      }

      return {
        'id': accountNo,
        'value': accountNo,
        'name': displayName,
        'label': displayLabel,
        'accountName': accountName,
        'accountNo': accountNo,
        'balance': balance,
      };
    }).toList();
  }

  /// Get filter summary for debugging
  static String getFilterSummary(List<String>? defaultValues) {
    if (defaultValues == null || defaultValues.isEmpty) {
      return "No filters applied";
    }

    Map<String, String> criteria = parseFilterCriteria(defaultValues);
    Map<String, dynamic> filters = buildAccountFilters(criteria);

    List<String> summary = [];
    filters.forEach((key, value) {
      summary.add("$key: $value");
    });

    return "Filters: ${summary.join(', ')}";
  }

  /// Set account type flags based on accountType and account name (restored original logic)
  static void setAccountTypeFlags(Map<String, dynamic> account) {
    String accountType = account['accountType']?.toString() ?? '';
    String accountName = account['accountName']?.toString().toLowerCase() ?? '';

    // Initialize all flags to 'No' first
    account['canWithdraw'] = 'No';
    account['canDeposit'] = 'No';
    account['isLoanAccount'] = 'No';
    account['isNWD'] = 'No';
    account['isSavingsAccount'] = 'No';
    account['isShareCapital'] = 'No';

    // Set canWithdraw flag - for now, assume all accounts can withdraw except loans
    // This may need to be adjusted based on your business logic
    if (accountType != '1' && !accountName.contains('loan')) {
      account['canWithdraw'] = 'Yes';
    } else {
      account['canWithdraw'] = 'No';
    }

    // Set canDeposit flag based on account types that can accept deposits
    // Following the flutter_configurable pattern: Savings, Share Capital, NWD, and Loan accounts can accept deposits
    if (accountType == '1' || accountName.contains('loan')) {
      // Loans can receive deposits for repayments
      account['canDeposit'] = 'Yes';
    } else if (accountType == '2' ||
        accountName.contains('nwd') ||
        accountName.contains('deposit')) {
      // NWD/Deposit accounts can accept deposits
      account['canDeposit'] = 'Yes';
    } else if (accountType == '3' || accountName.contains('savings')) {
      // Savings accounts can accept deposits
      account['canDeposit'] = 'Yes';
    } else if (accountType == '4' || accountName.contains('share')) {
      // Share capital accounts can accept deposits
      account['canDeposit'] = 'Yes';
    } else {
      // Other account types cannot accept deposits by default
      account['canDeposit'] = 'No';
    }

    // Set flags based on account type mapping
    switch (accountType) {
      case '1': // Loan accounts
        account['isLoanAccount'] = 'Yes';
        break;
      case '2': // NWD/Deposit accounts
        account['isNWD'] = 'Yes';
        break;
      case '3': // Savings accounts
        account['isSavingsAccount'] = 'Yes';
        break;
      case '4': // Share capital accounts
        account['isShareCapital'] = 'Yes';
        break;
    }

    // Additional name-based flag setting as fallback
    if (accountName.contains('savings') &&
        account['isSavingsAccount'] != 'Yes') {
      account['isSavingsAccount'] = 'Yes';
    }
    if (accountName.contains('share') && account['isShareCapital'] != 'Yes') {
      account['isShareCapital'] = 'Yes';
    }
    if (accountName.contains('loan') && account['isLoanAccount'] != 'Yes') {
      account['isLoanAccount'] = 'Yes';
    }
    if ((accountName.contains('nwd') || accountName.contains('deposit')) &&
        account['isNWD'] != 'Yes') {
      account['isNWD'] = 'Yes';
    }

    AppLogger.info(
        "Account flags set for ${account['accountName']}: canWithdraw=${account['canWithdraw']}, canDeposit=${account['canDeposit']}, Loan=${account['isLoanAccount']}, Savings=${account['isSavingsAccount']}, Share=${account['isShareCapital']}, NWD=${account['isNWD']}");
  }
}
