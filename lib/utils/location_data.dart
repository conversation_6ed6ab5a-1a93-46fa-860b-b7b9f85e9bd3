import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationData {
  static final Map<String, List<Map<String, dynamic>>> locations = {
    '81': [
      // Tower Sacco
      {
        'name': 'Co-op Bank ATM - CBD Branch',
        'position': const LatLng(-1.2848, 36.8219),
        'type': 'atm',
        'address': 'Co-operative House, Haile Selassie Avenue'
      },
      {
        'name': 'Co-op Bank ATM - Rongai',
        'position': const LatLng(-1.3960, 36.8752),
        'type': 'atm',
        'address': 'Rongai Town, Near Police Station'
      },
      {
        'name': 'Co-op Bank ATM - Westlands',
        'position': const LatLng(-1.2673, 36.8032),
        'type': 'atm',
        'address': 'Westlands Branch, Mpaka Road'
      },
      {
        'name': 'Tower SACCO - Head Office',
        'position': const LatLng(-1.0388, 37.0834),
        'type': 'branch',
        'address': 'Thika Town, KDIC Building'
      },
      {
        'name': 'Tower SACCO - Nairobi Branch',
        'position': const LatLng(-1.2815, 36.8185),
        'type': 'branch',
        'address': 'Nairobi CBD, Kimathi Street'
      }
    ],
    '38': [
      // Mentor Cash
      {
        'name': 'Mentor Cash - Nakuru Branch',
        'position': const LatLng(-0.3031, 36.0800),
        'type': 'branch',
        'address': 'Nakuru Town, Kenyatta Avenue'
      },
    ],
    '93': [
      // M-BORESHA
      {
        'name': 'M-BORESHA - Eldoret Branch',
        'position': const LatLng(0.5143, 35.2698),
        'type': 'branch',
        'address': 'Eldoret Town, Uganda Road'
      },
    ],
    '116': [
      // Amica Sacco
      {
        'name': 'Amica Sacco - Murang\'a Branch',
        'position': const LatLng(-0.7211, 37.1521),
        'type': 'branch',
        'address': 'Murang\'a Town, Uhuru Highway'
      },
    ],
    '120': [
      // Magadi Sacco
      {
        'name': 'Magadi Sacco - Magadi Branch',
        'position': const LatLng(-1.9000, 36.2833),
        'type': 'branch',
        'address': 'Magadi Town, Lake Magadi'
      },
    ],
    '999': [
      // Tangazoletu Sacco
      {
        'name': 'Tangazoletu - Head Office',
        'position': const LatLng(-1.286389, 36.817223),
        'type': 'branch',
        'address': 'Nairobi, Kenya'
      },
    ],
    '114': [
      // SHELLOYEES SACCO
      {
        'name': 'SHELLOYEES SACCO - Head Office',
        'position': const LatLng(-1.2921, 36.8219),
        'type': 'branch',
        'address': 'Nairobi CBD, Shell House'
      },
      // Additional branches and ATM locations to be added once confirmed
    ],
  };
}
