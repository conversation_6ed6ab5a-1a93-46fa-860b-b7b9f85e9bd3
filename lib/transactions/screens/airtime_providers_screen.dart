import 'package:flutter/material.dart';
import '../../utils/color_palette.dart';
import 'form_journey_screen.dart';

class AirtimeProvidersScreen extends StatelessWidget {
  final List<dynamic> accounts;

  const AirtimeProvidersScreen({
    super.key,
    required this.accounts,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDark ? Theme.of(context).scaffoldBackgroundColor : Colors.grey[200],
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Buy Airtime',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Network Provider',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDark ? Colors.white : ColorPalette.textDark,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView(
                children: [
                  _buildProviderTile(
                    context: context,
                    providerName: 'Safaricom',
                    providerIcon: Icons.phone_android,
                    providerColor: Colors.green,
                    isDark: isDark,
                  ),
                  const SizedBox(height: 15),
                  _buildProviderTile(
                    context: context,
                    providerName: 'Airtel',
                    providerIcon: Icons.phone_android,
                    providerColor: Colors.red,
                    isDark: isDark,
                  ),
                  const SizedBox(height: 15),
                  _buildProviderTile(
                    context: context,
                    providerName: 'Telkom',
                    providerIcon: Icons.phone_android,
                    providerColor: Colors.blue,
                    isDark: isDark,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProviderTile({
    required BuildContext context,
    required String providerName,
    required IconData providerIcon,
    required Color providerColor,
    required bool isDark,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          if (!isDark)
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: providerColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            providerIcon,
            color: providerColor,
            size: 24,
          ),
        ),
        title: Text(
          providerName,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : ColorPalette.textDark,
          ),
        ),
        subtitle: Text(
          'Buy $providerName airtime',
          style: TextStyle(
            fontSize: 14,
            color: isDark ? Colors.white70 : Colors.grey[600],
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: isDark ? Colors.white70 : Colors.grey[400],
          size: 16,
        ),
        onTap: () => _navigateToAirtimeForm(context, providerName),
      ),
    );
  }

  void _navigateToAirtimeForm(BuildContext context, String provider) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FormJourneyScreen(
          serviceCode: 'AT',
          accounts: accounts,
          tileResponse: {
            'provider': provider,
            'serviceType': 'airtime',
          },
        ),
      ),
    );
  }
}
