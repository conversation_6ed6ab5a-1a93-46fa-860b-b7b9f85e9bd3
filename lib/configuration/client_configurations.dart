import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'client_config.dart';
import '../utils/font_helper.dart';

// This file is responsible for registering all client configurations
class ClientConfigurations {
  // Private constructor to prevent instantiation
  ClientConfigurations._();

  // Register all known client configurations
  static void registerAllClients() {
    final manager = ClientThemeManager();

    // Register default client (999)
    _registerDefaultClient(manager);

    // Register Mentor <PERSON> (38)
    _registerMentorClient(manager);

    // Register Amica <PERSON> (116)
    _registerAmicaClient(manager);

    _registerTowerClient(manager);

    // Register Magadi <PERSON>cco (120)
    _registerMagadiClient(manager);

    // Register M-BORESHA (93)
    _registerMBoreshaClient(manager);

    // Register Shelloyees Sacco (114)
    _registerShelloyeesClient(manager);

    // Register Ollin <PERSON> (54)
    _registerOllinClient(manager);

    // Register additional clients as needed
    // You can also call registerDynamicClients() here for other IDs
  }

  // Tangazoletu Sacco configuration (Client ID: 999)
  static void _registerDefaultClient(ClientThemeManager manager) {
    manager.registerClientConfig(
        '999',
        ClientConfig(
          clientId: '999',
          displayName: 'Tangazoletu Sacco',
          colors: ClientColorPalette(
            primary: Color(0xFF3987CA), // Blue primary
            secondary: Color(0xFF8CC543), // Green secondary
            background: Color(0xFFF5F5F5),
            surface: Color(0xFFFFFFFF),
            error: Color(0xFFD32F2F),
            success: Color(0xFF43A047),
            warning: Color(0xFFFFA000),
            info: Color(0xFF1976D2),
            textPrimary: Color(0xFF212121),
            textSecondary: Color(0xFF757575),
            textDisabled: Color(0xFFBDBDBD),
            modalBackground: Color(0xFF6B4779), // Consistent modal background
            unselectedNavItem: Color(0xFF6B4779), // Consistent unselected nav
          ),
          logoAsset: 'assets/logos/999/logo.png',
          fontFamily: FontHelper.interFontFamily,
          splashConfig: SplashScreenConfig(
            type: "image",
            assetPath: 'assets/images/999/splash.png',
            duration: const Duration(seconds: 3),
            fallbackImagePath: 'assets/logos/999/logo.png',
          ),
          appIdleTimeout: 120000,
        ));
  }

  static void _registerShelloyeesClient(ClientThemeManager manager) {
    manager.registerClientConfig(
        '114',
        ClientConfig(
          clientId: '114',
          displayName: 'SHELLOYEES SACCO',
          colors: ClientColorPalette(
            secondary: Color(0xFF438b3f),
            primary: Color(0xFFF43A27),
            background: Color(0xFFF5F5F5),
            surface: Color(0xFFFFFFFF),
            error: Color(0xFFD32F2F),
            success: Color(0xFF43A047),
            warning: Color(0xFFFFA000),
            info: Color(0xFF1976D2),
            textPrimary: Color(0xFF212121),
            textSecondary: Color(0xFF757575),
            textDisabled: Color(0xFFBDBDBD),
            modalBackground: Color(0xFF6B4779),
            unselectedNavItem: Color(0xFF6B4779),
          ),
          logoAsset: 'assets/logos/114/logo.png',
          fontFamily: GoogleFonts.lato().fontFamily!,
          splashConfig: SplashScreenConfig(
            type: "image",
            assetPath: 'assets/images/114/splash.png',
            duration: const Duration(seconds: 3),
            fallbackImagePath: 'assets/logos/114/logo.png',
          ),
          appIdleTimeout: 120000,
        ));
  }

  static void _registerTowerClient(ClientThemeManager manager) {
    manager.registerClientConfig(
        '81',
        ClientConfig(
          clientId: '81',
          displayName: 'Tower Sacco',
          colors: ClientColorPalette(
            primary: Color(0xFF00A651), // Original green primary
            secondary: Color(0xFFEC008C), // Original pink secondary
            background: Color(0xFFF5F5F5),
            surface: Color(0xFFFFFFFF),
            error: Color(0xFFD32F2F),
            success: Color(0xFF43A047),
            warning: Color(0xFFFFA000),
            info: Color(0xFF1976D2),
            textPrimary: Color(0xFF212121),
            textSecondary: Color(0xFF757575),
            textDisabled: Color(0xFFBDBDBD),
            modalBackground: Color(0xFF6B4779), // Consistent modal background
            unselectedNavItem: Color(0xFF6B4779), // Consistent unselected nav
          ),
          logoAsset: 'assets/logos/logo.png',
          fontFamily: GoogleFonts.lato().fontFamily!,
          splashConfig: SplashScreenConfig(
            type: "image",
            assetPath: 'assets/images/splash.png',
            duration: const Duration(seconds: 3),
          ),
          appIdleTimeout: 120000,
        ));
  }

  // Mentor Sacco configuration (Client ID: 38)
  static void _registerMentorClient(ClientThemeManager manager) {
    manager.registerClientConfig(
        '38',
        ClientConfig(
          clientId: '38',
          displayName: 'Mentor Cash',
          colors: ClientColorPalette(
            primary: Color(0xFFF38B32), // Orange
            secondary: Color(0xFF067C4B), // Green
            background: Color(0xFFF5F5F5),
            surface: Color(0xFFFFFFFF),
            error: Color(0xFFD32F2F),
            success: Color(0xFF43A047),
            warning: Color(0xFFFFA000),
            info: Color(0xFF1976D2),
            textPrimary: Color(0xFF212121),
            textSecondary: Color(0xFF757575),
            textDisabled: Color(0xFFBDBDBD),
            modalBackground: Color(0xFF6B4779), // Consistent modal background
            unselectedNavItem: Color(0xFF6B4779), // Consistent unselected nav
          ),
          logoAsset: 'assets/logos/38/logo.png',
          fontFamily: FontHelper.interFontFamily,
          splashConfig: SplashScreenConfig(
            type: "image",
            assetPath: 'assets/images/38/splash.png',
            duration: const Duration(seconds: 3),
            fallbackImagePath: 'assets/logos/38/logo.png',
          ),
          appIdleTimeout: 120000,
        ));
  }

  // Amica Sacco configuration (Client ID: 116)
  static void _registerAmicaClient(ClientThemeManager manager) {
    manager.registerClientConfig(
        '116',
        ClientConfig(
          clientId: '116',
          displayName: 'Amica Sacco',
          colors: ClientColorPalette(
            primary: Color(0xFF2648B6), // Blue
            secondary: Color(0xFF87CEFA), // Light Blue
            background: Color(0xFFF5F5F5),
            surface: Color(0xFFFFFFFF),
            error: Color(0xFFD32F2F),
            success: Color(0xFF43A047),
            warning: Color(0xFFFFA000),
            info: Color(0xFF1976D2),
            textPrimary: Color(0xFF212121),
            textSecondary: Color(0xFF757575),
            textDisabled: Color(0xFFBDBDBD),
            modalBackground: Color(0xFF6B4779), // Consistent modal background
            unselectedNavItem: Color(0xFF6B4779), // Consistent unselected nav
          ),
          logoAsset: 'assets/logos/116/logo.png',
          fontFamily: FontHelper.interFontFamily,
          splashConfig: SplashScreenConfig(
            type:
                "image", // Can be changed to "video" if you have splash videos
            assetPath: 'assets/images/116/splash.png',
            duration: const Duration(seconds: 3),
            fallbackImagePath: 'assets/logos/116/logo.png',
          ),
          appIdleTimeout: 120000,
        ));
  }

  // Magadi Sacco configuration (Client ID: 120)
  static void _registerMagadiClient(ClientThemeManager manager) {
    manager.registerClientConfig(
        '120',
        ClientConfig(
          clientId: '120',
          displayName: 'Magadi Sacco',
          colors: ClientColorPalette(
            secondary: Color(0xFF234e87), // Primary color #234e87
            primary: Color(0xFF9b3915), // Secondary color #9b3915
            background: Color(0xFFF5F5F5),
            surface: Color(0xFFFFFFFF),
            error: Color(0xFFD32F2F),
            success: Color(0xFF43A047),
            warning: Color(0xFFFFA000),
            info: Color(0xFF1976D2),
            textPrimary: Color(0xFF212121),
            textSecondary: Color(0xFF757575),
            textDisabled: Color(0xFFBDBDBD),
            modalBackground: Color(0xFF234e87), // Consistent modal background
            unselectedNavItem: Color(0xFF234e87), // Consistent unselected nav
          ),
          logoAsset: 'assets/logos/120/logo.png',
          fontFamily: FontHelper.poppinsFontFamily,
          splashConfig: SplashScreenConfig(
            type: "image",
            assetPath: 'assets/images/120/splash.png',
            duration: const Duration(seconds: 3),
            fallbackImagePath: 'assets/logos/120/logo.png',
          ),
          appIdleTimeout: 120000,
        ));
  }

  // M-BORESHA configuration (Client ID: 93)
  static void _registerMBoreshaClient(ClientThemeManager manager) {
    manager.registerClientConfig(
        '93',
        ClientConfig(
          clientId: '93',
          displayName: 'M-BORESHA',
          colors: ClientColorPalette(
            secondary: Color(0xFF388515), // Primary color #388515
            primary: Color(0xFFB1D848), // Secondary color #B1D848
            background: Color(0xFFF5F5F5),
            surface: Color(0xFFFFFFFF),
            error: Color(0xFFD32F2F),
            success: Color(0xFF43A047),
            warning: Color(0xFFFFA000),
            info: Color(0xFF1976D2),
            textPrimary: Color(0xFF212121),
            textSecondary: Color(0xFF757575),
            textDisabled: Color(0xFFBDBDBD),
            modalBackground: Color(0xFF6B4779), // Consistent modal background
            unselectedNavItem: Color(0xFF6B4779), // Consistent unselected nav
          ),
          logoAsset: 'assets/logos/93/logo.png',
          fontFamily: FontHelper.interFontFamily,
          splashConfig: SplashScreenConfig(
            type: "image",
            assetPath: 'assets/images/93/splash.png',
            duration: const Duration(seconds: 3),
            fallbackImagePath: 'assets/logos/93/logo.png',
          ),
          appIdleTimeout: 120000,
        ));
  }

  // Ollin Sacco configuration (Client ID: 54)
  static void _registerOllinClient(ClientThemeManager manager) {
    manager.registerClientConfig(
        '54',
        ClientConfig(
          clientId: '54',
          displayName: 'Ollin Sacco',
          colors: ClientColorPalette(
            secondary: Color(0xFF00AEEF), // Blue primary #00AEEF
            primary: Color(0xFFF58220), // Orange secondary #F58220
            background: Color(0xFFF5F5F5),
            surface: Color(0xFFFFFFFF),
            error: Color(0xFFD32F2F),
            success: Color(0xFF43A047),
            warning: Color(0xFFFFA000),
            info: Color(0xFF1976D2),
            textPrimary: Color(0xFF212121),
            textSecondary: Color(0xFF757575),
            textDisabled: Color(0xFFBDBDBD),
            modalBackground: Color(0xFF6B4779), // Consistent modal background
            unselectedNavItem: Color(0xFF6B4779), // Consistent unselected nav
          ),
          logoAsset: 'assets/logos/54/logo.png',
          fontFamily: FontHelper.interFontFamily,
          splashConfig: SplashScreenConfig(
            type: "image",
            assetPath: 'assets/images/54/splash.png',
            duration: const Duration(seconds: 3),
            fallbackImagePath: 'assets/logos/54/logo.png',
          ),
          appIdleTimeout: 120000,
        ));
  }

  // Register a dynamic client based on CLIENT_ID
  static void registerDynamicClient(String clientId) {
    final manager = ClientThemeManager();

    // Skip if already registered
    if (manager.hasClient(clientId)) {
      return;
    }

    // Create a dynamic configuration
    manager.registerClientConfig(
        clientId,
        ClientConfig(
          clientId: clientId,
          displayName: 'Client $clientId',
          colors: ClientColorPalette(
            primary: Color(0xFF6B4E71), // Default purple
            secondary: Color(0xFF8A6B8F), // Default secondary
            background: Color(0xFFF5F5F5),
            surface: Color(0xFFFFFFFF),
            error: Color(0xFFD32F2F),
            success: Color(0xFF43A047),
            warning: Color(0xFFFFA000),
            info: Color(0xFF1976D2),
            textPrimary: Color(0xFF212121),
            textSecondary: Color(0xFF757575),
            textDisabled: Color(0xFFBDBDBD),
            modalBackground: Color(0xFF6B4779), // Consistent modal background
            unselectedNavItem: Color(0xFF6B4779), // Consistent unselected nav
          ),
          logoAsset: 'assets/logos/$clientId/logo.png',
          fontFamily: FontHelper.interFontFamily,
          splashConfig: SplashScreenConfig(
            type: "image",
            assetPath: 'assets/images/$clientId/splash.png',
            duration: const Duration(seconds: 3),
            fallbackImagePath: 'assets/logos/logo.png',
          ),
          appIdleTimeout: 120000,
        ));
  }
}
